from flask import Flask, request, jsonify, g
import sqlite3
import os
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import jwt
from datetime import datetime, timedelta

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'  # 生产环境应该使用更安全的密钥
app.config['DATABASE'] = 'users.db'

# 数据库连接
def get_db():
    if 'db' not in g:
        g.db = sqlite3.connect(app.config['DATABASE'])
        g.db.row_factory = sqlite3.Row
    return g.db

@app.teardown_appcontext
def close_db(e=None):
    db = g.pop('db', None)
    if db is not None:
        db.close()

# 创建用户表
with app.app_context():
    db = get_db()
    db.execute('''
        CREATE TABLE IF NOT EXISTS user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            create_time TEXT NOT NULL,
            update_time TEXT
        )
    ''')
    db.commit()

# 用户模型
class User:
    def __init__(self, id, username, password, email, create_time, update_time=None):
        self.id = id
        self.username = username
        self.password = password
        self.email = email
        self.create_time = create_time
        self.update_time = update_time

    @staticmethod
    def get(user_id):
        db = get_db()
        user = db.execute('SELECT * FROM user WHERE id = ?', (user_id,)).fetchone()
        if not user:
            return None
        return User(**user)
    
    @staticmethod
    def get_by_username(username):
        db = get_db()
        user = db.execute('SELECT * FROM user WHERE username = ?', (username,)).fetchone()
        if not user:
            return None
        return User(**user)
    
    @staticmethod
    def create(username, password, email):
        db = get_db()
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        hashed_password = generate_password_hash(password, method='sha256')
        try:
            cursor = db.execute(
                'INSERT INTO user (username, password, email, create_time, update_time) VALUES (?, ?, ?, ?, ?)',
                (username, hashed_password, email, current_time, current_time)
            )
            db.commit()
            user_id = cursor.lastrowid
            return User.get(user_id)
        except sqlite3.IntegrityError as e:
            db.rollback()
            raise e
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'createTime': self.create_time,
            'updateTime': self.update_time or self.create_time
        }

# JWT工具函数
def create_token(user_id):
    payload = {
        'exp': datetime.utcnow() + timedelta(days=1),
        'iat': datetime.utcnow(),
        'sub': user_id
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')

def verify_token(token):
    try:
        payload = jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        return payload['sub']
    except jwt.ExpiredSignatureError:
        return 'Token expired. Please log in again.'
    except jwt.InvalidTokenError:
        return 'Invalid token. Please log in again.'

# 认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(" ")[1]
        
        if not token:
            return jsonify({'code': 401, 'msg': 'Token is missing!'}), 401
        
        user_id = verify_token(token)
        if not isinstance(user_id, int):
            return jsonify({'code': 401, 'msg': user_id}), 401
        
        current_user = User.get(user_id)
        if not current_user:
            return jsonify({'code': 401, 'msg': 'User not found'}), 401
            
        return f(current_user, *args, **kwargs)
    return decorated

# 用户登录
@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({'code': 400, 'msg': '用户名或密码不能为空'}), 400
    
    user = User.get_by_username(data['username'])
    if not user or not check_password_hash(user.password, data['password']):
        return jsonify({'code': 400, 'msg': '用户名或密码错误'}), 400
    
    token = create_token(user.id)
    user_info = user.to_dict()
    user_info['token'] = token
    
    return jsonify({
        'code': 200,
        'msg': '登录成功',
        'data': user_info
    })

# 用户注册
@app.route('/api/register', methods=['POST'])
def register():
    data = request.get_json()
    if not data or 'username' not in data or 'password' not in data or 'email' not in data:
        return jsonify({'code': 400, 'msg': '请提供完整信息'}), 400
    
    try:
        user = User.create(data['username'], data['password'], data['email'])
        return jsonify({
            'code': 200,
            'msg': '注册成功',
            'data': user.to_dict()
        })
    except sqlite3.IntegrityError:
        return jsonify({'code': 400, 'msg': '用户名或邮箱已存在'}), 400

# 获取所有用户
@app.route('/api/users', methods=['GET'])
@token_required
def get_users(current_user):
    db = get_db()
    users = db.execute('SELECT * FROM user').fetchall()
    user_list = [dict(user) for user in users]
    # 移除密码等敏感信息
    for user in user_list:
        user.pop('password', None)
        user['createTime'] = user.pop('create_time')
        user['updateTime'] = user.pop('update_time', user['createTime'])
    
    return jsonify({
        'code': 200,
        'msg': 'success',
        'data': user_list
    })

# 更新用户
@app.route('/api/users/<int:user_id>', methods=['PUT'])
@token_required
def update_user(current_user, user_id):
    if current_user.id != user_id:
        return jsonify({'code': 403, 'msg': '没有权限修改该用户信息'}), 403
    
    data = request.get_json()
    if not data:
        return jsonify({'code': 400, 'msg': '没有提供更新数据'}), 400
    
    update_fields = []
    params = {}
    
    if 'username' in data:
        update_fields.append('username = :username')
        params['username'] = data['username']
    if 'email' in data:
        update_fields.append('email = :email')
        params['email'] = data['email']
    if 'password' in data:
        update_fields.append('password = :password')
        params['password'] = generate_password_hash(data['password'], method='sha256')
    
    if not update_fields:
        return jsonify({'code': 400, 'msg': '没有有效的更新字段'}), 400
    
    update_fields.append('update_time = :update_time')
    params['update_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    params['id'] = user_id
    
    db = get_db()
    try:
        query = f"UPDATE user SET {', '.join(update_fields)} WHERE id = :id"
        db.execute(query, params)
        db.commit()
        
        # 获取更新后的用户信息
        user = User.get(user_id)
        return jsonify({
            'code': 200,
            'msg': '更新成功',
            'data': user.to_dict()
        })
    except sqlite3.IntegrityError as e:
        return jsonify({'code': 400, 'msg': '更新失败，用户名或邮箱已存在'}), 400

# 删除用户
@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@token_required
def delete_user(current_user, user_id):
    if current_user.id != user_id and current_user.username != 'admin':
        return jsonify({'code': 403, 'msg': '没有权限删除该用户'}), 403
    
    db = get_db()
    try:
        db.execute('DELETE FROM user WHERE id = ?', (user_id,))
        db.commit()
        return jsonify({
            'code': 200,
            'msg': '删除成功'
        })
    except Exception as e:
        return jsonify({'code': 500, 'msg': '删除用户失败'}), 500

if __name__ == '__main__':
    app.run(debug=True)
